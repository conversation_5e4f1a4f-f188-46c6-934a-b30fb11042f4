# Overview

This document defines the structural hierarchy of the SIT Platform, covering modules, assignments, assignment groups, chat architecture, and threaded discussions.
The purpose is to establish a standardized model to implement and integrate related features

# Entity Hierarchy and Relationships

a. Module Level

A Module represents a course or subject.

Each module contains one or more Assignments.

A student can be enrolled in multiple modules simultaneously.

An instructor can be assigned to manage multiple modules.

b. Assignment Level

Each Assignment belongs to exactly one Module.

A module can contain multiple assignments at the same time.

An assignment is created for all students enrolled in the module.

Assignments have separate deadlines, file repositories, and group allocations.

c. Assignment Group Level

Within each assignment, students are divided into Assignment Groups.

Each student can belong to only one group per assignment.

A group is unique to a specific assignment and does not persist across assignments.

Each assignment can contain multiple groups.

Instructors can be assigned to one or more groups within an assignment.

d. Chat Level

Each Assignment Group has one dedicated Group Chat.

The Group Chat is scoped to the assignment group only and is not shared across groups or assignments.

Both students and instructors (assigned to the group) can send messages and upload files in this chat.

Instructors are tagged when they post in group chats.

e. Thread Level

Within each Group Chat, users can create multiple threads.

Threads are used to organize discussions by topic.

Each thread belongs to a single Group Chat.

Files uploaded in threads are associated with the thread and also surfaced in the "Temporary" file repository of the assignment group.

# Instructors - File Manangement

Description: As an Instructor, I want to control the visibility, categorization, and metadata of my files, so that I can manage my assignment materials efficiently and release them to students at the appropriate time.

FR1: File Visibility Button

The system must provide the Instructor with a mechanism (button) to change a file's visibility status between Hidden and Visible.

FR2: Assignment-Level Organization

The Instructor's file repository interface must be organized at the Assignment level.

FR3: File Repository

Within the Instructor's file repository, the file list must be divided into two default repositories: Temporary files and Permanent files.

The Temporary files: must aggregate all files sent via chat from all Assignment Groups belonging to that assignment.
The Permanent files: the instructor can upload files here
FR4: File Tagging

The system must allow an Instructor to add a Tag (optional) for each file.
The list of tags will be automatically generated based on the names of the existing threads or conversations - each thread name will appear as a tag option in the list
FR5: File Description

The system must allow an Instructor to add or edit a Description for each file, with a maximum length of 120 characters.

FR6: The file list displayed to Instructors must include the following columns:

Name, Uploaded by, Last modified, File size, Hidden/Visible, Tag, Description, Download

FR7: Download Functionality

When clicked, the file shall be downloaded directly to the user's device using the browser's default download behavior.

No file preview, thumbnail, or modal view is required for this MVP phase

# Instructor - File management - Delete

Description: As an Instructor, I want to permanently delete a specific file that I have uploaded to the "Permanent files" repository, so that I can remove outdated, incorrect, or irrelevant materials and keep my course content clean and up-to-date.

Functional Requirements

FR1: Hard Delete Mechanic

When a file deletion is executed, the system must perform a hard delete, permanently removing the file and its associated metadata from the system storage entirely.

FR2: Deletion Confirmation

Before executing a delete action, the system must display a confirmation dialog ("Are you sure you want to permanently delete this file? This action cannot be undone.") to prevent accidental data loss.

FR3: Permission to Delete - Ownership Rule

A user (Instructor or Student) must only be able to delete files that they have uploaded by themselves (i.e., they are the file owner).

The system must prevent users from deleting files they do not own. Specifically, a ztudent cannot delete files uploaded by an Instructor or another student, and vice versa.

FR4: Instructor Deletion Access

The system must provide a "Delete" option for Instructors on all files within their Permanent files repository.

FR5: Student Deletion Access

The system must provide a "Delete" option for Students only on files within their Permanent repository.

FR6: Prohibition on Deleting Temporary Files

The system must not provide a manual "Delete" option for any file that is currently classified as Temporary. These files are only removed via the automatic 180-day lifecycle rule.

FR9: UI State After Deletion

Upon successful deletion, the file must be immediately removed from the file list interface for all users who previously had access to it.

# Instructor - File management - File Upload

Description: As an Instructor, I want to upload files in different ways (formally for an assignment or informally via chat), so that I can provide both official, long-term materials and quick, temporary references

Functional Requirement

FR1: Assignment File Upload

The system must allow an Instructor to upload files at the Assignment level.

FR2: File Upload Methods and Classification

The instructors can upload files in 2 ways:

Via File Management: the system must automatically classify that file as Permanent
Uploaded as an attachment in Chat: the system must automatically classify that file as Temporary
FR3: Temporary File Deletion Policy

Files classified as Temporary must be automatically deleted by the system after 180 days from the upload date.

FR4: Default File Visibility

All uploaded files, regardless of the method, must have a default visibility status of Hidden.

Then the instructor can change to visible.

# Student - File management

Description: As a Student, I want to easily find all relevant, visible assignment files for my assignment group, so that I have the necessary resources to complete my work

FR1: File Visibility by Status

The system must only display files to Students that have a status of Visible.

FR2: File Visibility by Assignment Group

Students can only view files that belong to the Assignment Group they are part of.
In other words, they cannot access files uploaded in other assignment groups.
FR3: File Repositories

The system must provide Students with a file management screen featuring three default repositories: Assignment, Temporary, and Permanent.

The Assignment repository: Must display all files uploaded by the Instructor as Permanent that are marked as Visible.
The Temporary repository: Must aggregate all files sent via chat within the Student's specific Assignment Group
The Permanent repository: Must display all files uploaded by the Student themself
FR4: Student File Upload

The system must provide a file upload function for Students. These files will be saved to their Permanent repository

FR5: The file list displayed to Students (in all three repositories) must include the following columns:

Name, Uploaded by, Last modified, File size, Tag, Description.
