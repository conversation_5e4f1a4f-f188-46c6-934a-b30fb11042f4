import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableForeignKey,
} from 'typeorm';

export class AddCourseAndAssignmentGroupToDocuments1739438585000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add course_id column
    await queryRunner.addColumn(
      'documents',
      new TableColumn({
        name: 'course_id',
        type: 'uuid',
        isNullable: false,
      }),
    );

    // Add assignment_group_id column
    await queryRunner.addColumn(
      'documents',
      new TableColumn({
        name: 'assignment_group_id',
        type: 'uuid',
        isNullable: false,
      }),
    );

    // Create foreign key constraint for course_id
    await queryRunner.createForeignKey(
      'documents',
      new TableForeignKey({
        columnNames: ['course_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'courses',
        onDelete: 'CASCADE',
      }),
    );

    // Create foreign key constraint for assignment_group_id
    await queryRunner.createForeignKey(
      'documents',
      new TableForeignKey({
        columnNames: ['assignment_group_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'group_assignments',
        onDelete: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    const table = await queryRunner.getTable('documents');

    const courseForeignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('course_id') !== -1,
    );
    if (courseForeignKey) {
      await queryRunner.dropForeignKey('documents', courseForeignKey);
    }

    const assignmentGroupForeignKey = table.foreignKeys.find(
      (fk) => fk.columnNames.indexOf('assignment_group_id') !== -1,
    );
    if (assignmentGroupForeignKey) {
      await queryRunner.dropForeignKey('documents', assignmentGroupForeignKey);
    }

    // Drop columns
    await queryRunner.dropColumn('documents', 'course_id');
    await queryRunner.dropColumn('documents', 'assignment_group_id');
  }
}
