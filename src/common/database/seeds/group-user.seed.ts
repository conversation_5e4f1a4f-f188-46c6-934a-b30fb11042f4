import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@src/modules/assignment-groups/entities/assignment-group.entity';
import { GroupUser } from '@modules/group-users/entities/group-user.entity';

export async function seedGroupUsers(
  dataSource: DataSource,
  users: User[],
  groupAssignments: GroupAssignment[],
): Promise<GroupUser[]> {
  console.log('🌱 Seeding group users...');
  const groupUserRepository = dataSource.getRepository(GroupUser);

  // Check if group users already exist
  const existingGroupUsers = await groupUserRepository.find();
  if (existingGroupUsers.length > 0) {
    console.log('✅ Group users already exist, skipping group user seeding');
    return existingGroupUsers;
  }

  const instructor = users.find((u) => u.name === 'Dr. <PERSON>')!;
  const student1 = users.find((u) => u.name === '<PERSON>')!;
  const student2 = users.find((u) => u.name === 'Watson')!;

  const groupUsers = [
    // <PERSON><PERSON> <PERSON> as leader of first group assignment
    {
      id: uuidv4(),
      role: 'leader',
      groupId: groupAssignments[0].id,
      userId: instructor.id,
    },
    // Alex as member of first group assignment
    {
      id: uuidv4(),
      role: 'member',
      groupId: groupAssignments[0].id,
      userId: student1.id,
    },
    // Watson as member of first group assignment
    {
      id: uuidv4(),
      role: 'member',
      groupId: groupAssignments[0].id,
      userId: student2.id,
    },
    // Alex as leader of second group assignment
    {
      id: uuidv4(),
      role: 'leader',
      groupId: groupAssignments[1].id,
      userId: student1.id,
    },
    // Watson as member of second group assignment
    {
      id: uuidv4(),
      role: 'member',
      groupId: groupAssignments[1].id,
      userId: student2.id,
    },
    // Dr. John as mentor of second group assignment
    {
      id: uuidv4(),
      role: 'mentor',
      groupId: groupAssignments[1].id,
      userId: instructor.id,
    },
    // Watson as leader of third group assignment
    {
      id: uuidv4(),
      role: 'leader',
      groupId: groupAssignments[2].id,
      userId: student2.id,
    },
    // Alex as member of third group assignment
    {
      id: uuidv4(),
      role: 'member',
      groupId: groupAssignments[2].id,
      userId: student1.id,
    },
    // Dr. John as mentor of third group assignment
    {
      id: uuidv4(),
      role: 'mentor',
      groupId: groupAssignments[2].id,
      userId: instructor.id,
    },
  ];

  const savedGroupUsers = await groupUserRepository.save(groupUsers);
  console.log(`✅ Created ${savedGroupUsers.length} group users`);
  return savedGroupUsers;
}
