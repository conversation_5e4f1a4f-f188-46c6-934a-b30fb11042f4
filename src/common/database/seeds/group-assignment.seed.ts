import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { Course } from '@modules/courses/entities/course.entity';
import { GroupAssignment } from '@src/modules/assignment-groups/entities/assignment-group.entity';

export async function seedGroupAssignments(
  dataSource: DataSource,
  users: User[],
  courses: Course[],
): Promise<GroupAssignment[]> {
  console.log('🌱 Seeding group assignments...');
  const groupAssignmentRepository = dataSource.getRepository(GroupAssignment);

  // Check if group assignments already exist
  const existingGroupAssignments = await groupAssignmentRepository.find();
  if (existingGroupAssignments.length > 0) {
    console.log(
      '✅ Group assignments already exist, skipping group assignment seeding',
    );
    return existingGroupAssignments;
  }

  const instructor = users.find((u) => u.name === 'Dr. John')!;

  const groupAssignments = [
    {
      id: uuidv4(),
      name: 'Final Project - E-commerce Platform',
      description:
        'Build a full-stack e-commerce platform with modern technologies including React, Node.js, and PostgreSQL. Focus on scalability, security, and user experience.',
      createdBy: instructor.id,
      courseId: courses[0].id,
    },
    {
      id: uuidv4(),
      name: 'Database Design Project - Library Management',
      description:
        'Design and implement a comprehensive library management system database. Include proper normalization, indexing strategies, and complex queries.',
      createdBy: instructor.id,
      courseId: courses[1].id,
    },
    {
      id: uuidv4(),
      name: 'ML Model Development - Predictive Analytics',
      description:
        'Develop and train machine learning models for predictive analytics. Implement data preprocessing, feature engineering, model selection, and evaluation metrics.',
      createdBy: instructor.id,
      courseId: courses[2].id,
    },
  ];

  const savedGroupAssignments =
    await groupAssignmentRepository.save(groupAssignments);
  console.log(`✅ Created ${savedGroupAssignments.length} group assignments`);
  return savedGroupAssignments;
}
