// src/database/seeds/run-seed.ts
import { AppDataSource } from '../data-source';
import { seedUsers } from './user.seed';
import { seedCourses } from './course.seed';
import { seedCourseUsers } from './course-user.seed';
import { seedGroupAssignments } from './group-assignment.seed';
import { seedGroupUsers } from './group-user.seed';
import { seedGroupAssignmentCourses } from './group-assignment-course.seed';
import { seedDocuments } from './document.seed';
import { runMongoSeeds } from '../mongodb/seeds/run-seed';
import mongoose from 'mongoose';

async function run() {
  console.log('🚀 Starting database seeding...');

  try {
    const dataSource = await AppDataSource.initialize();
    console.log('✅ Database connection established');

    // Seed data in the correct order (respecting foreign key dependencies)
    const users = await seedUsers(dataSource);
    const courses = await seedCourses(dataSource);
    const courseUsers = await seedCourseUsers(dataSource, users, courses);
    const groupAssignments = await seedGroupAssignments(
      dataSource,
      users,
      courses,
    );
    const groupUsers = await seedGroupUsers(
      dataSource,
      users,
      groupAssignments,
    );
    const groupAssignmentCourses = await seedGroupAssignmentCourses(
      dataSource,
      users,
      courses,
      groupAssignments,
    );

    console.log('\n🎉 PostgreSQL database seeding completed successfully!');
    console.log('\n📊 PostgreSQL Summary:');
    console.log(`   👥 Users: ${users.length}`);
    console.log(`   📚 Courses: ${courses.length}`);
    console.log(`   🔗 Course-User relationships: ${courseUsers.length}`);
    console.log(`   📋 Group assignments: ${groupAssignments.length}`);
    console.log(`   👤 Group users: ${groupUsers.length}`);
    console.log(
      `   📖 Group assignment courses: ${groupAssignmentCourses.length}`,
    );

    // Keep PostgreSQL connection open for MongoDB seeding
    console.log('\n🚀 Starting MongoDB seeding...');

    try {
      // Run MongoDB seeds (this will use the existing PostgreSQL connection)
      await runMongoSeeds(false); // Run in non-standalone mode
      console.log('✅ MongoDB seeding completed successfully!');

      // Seed documents AFTER MongoDB seeding to get real threadIds
      console.log('\n🔄 Seeding documents with real threadIds...');
      const documents = await seedDocuments(
        dataSource,
        users,
        groupAssignments,
      );
      console.log(`   📄 Documents: ${documents.length}`);

      // Close MongoDB connection after document seeding
      await mongoose.connection.close();
      console.log('✅ MongoDB connection closed');
    } catch (mongoErr) {
      console.error('❌ Error while running MongoDB seeders:', mongoErr);
      // Close MongoDB connection even if there was an error
      try {
        await mongoose.connection.close();
        console.log('✅ MongoDB connection closed');
      } catch (closeErr) {
        console.error('❌ Error closing MongoDB connection:', closeErr);
      }
    }

    await dataSource.destroy();
    console.log('✅ PostgreSQL connection closed');

    console.log('\n🎉 All database seeding completed successfully!');
    process.exit(0);
  } catch (err) {
    console.error('❌ Error while running seeders:', err);
    process.exit(1);
  }
}

void run();
