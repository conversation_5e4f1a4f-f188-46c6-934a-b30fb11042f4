import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@src/modules/assignment-groups/entities/assignment-group.entity';
import {
  Thread,
  ThreadDocument,
} from '@modules/threads/entities/thread.entity';

export async function seedThreads(
  users: User[],
  groupAssignments: GroupAssignment[],
) {
  console.log('🌱 Seeding threads...');

  // Check if threads already exist
  const existingThreads = await Thread.find();
  if (existingThreads.length > 0) {
    console.log('✅ Threads already exist, skipping thread seeding');
    return existingThreads;
  }

  const instructor = users.find((u) => u.isInstructor)!;
  const students = users.filter((u) => !u.isInstructor);

  if (!instructor) {
    throw new Error('No instructor found in database');
  }

  if (students.length === 0) {
    throw new Error('No students found in database');
  }

  const threadsData = [
    // Group 1 threads - First group assignment
    {
      title: 'Project Planning & Architecture Discussion',
      description: `Let's discuss the overall architecture and plan for our ${groupAssignments[0]?.name || 'project'}.`,
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, students[0]?.id, students[1]?.id].filter(
        Boolean,
      ),
      createdBy: instructor.id,
    },
    {
      title: 'Frontend Development Coordination',
      description:
        'Coordination thread for frontend development tasks and React components.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [students[0]?.id, students[1]?.id, students[2]?.id].filter(
        Boolean,
      ),
      createdBy: students[0]?.id,
    },
    {
      title: 'Backend API Development',
      description:
        'Discussion about backend API endpoints, database schema, and implementation.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, students[1]?.id, students[2]?.id].filter(
        Boolean,
      ),
      createdBy: students[1]?.id,
    },
    {
      title: 'Testing & Quality Assurance',
      description:
        'Thread for discussing testing strategies, QA processes, and bug reports.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, students[0]?.id, students[2]?.id].filter(
        Boolean,
      ),
      createdBy: instructor.id,
    },

    // Group 2 threads - Second group assignment (if exists)
    ...(groupAssignments[1]
      ? [
          {
            title: 'Database Schema Design',
            description: `Let's design the database schema for our ${groupAssignments[1].name}.`,
            groupAssignmentId: groupAssignments[1].id,
            threadUserIds: [
              instructor.id,
              students[1]?.id,
              students[2]?.id,
            ].filter(Boolean),
            createdBy: instructor.id,
          },
          {
            title: 'Data Migration & Optimization',
            description:
              'Discussion about data migration strategies and database optimization.',
            groupAssignmentId: groupAssignments[1].id,
            threadUserIds: [
              students[1]?.id,
              students[2]?.id,
              students[0]?.id,
            ].filter(Boolean),
            createdBy: students[1]?.id,
          },
          {
            title: 'Performance Analysis',
            description:
              'Analyzing system performance and identifying bottlenecks.',
            groupAssignmentId: groupAssignments[1].id,
            threadUserIds: [
              instructor.id,
              students[0]?.id,
              students[1]?.id,
            ].filter(Boolean),
            createdBy: students[0]?.id,
          },
        ]
      : []),

    // Group 3 threads - Third group assignment (if exists)
    ...(groupAssignments[2]
      ? [
          {
            title: 'ML Model Development',
            description: `Discussion about machine learning model development for ${groupAssignments[2].name}.`,
            groupAssignmentId: groupAssignments[2].id,
            threadUserIds: [
              instructor.id,
              students[0]?.id,
              students[2]?.id,
            ].filter(Boolean),
            createdBy: instructor.id,
          },
          {
            title: 'Data Preprocessing & Feature Engineering',
            description:
              'Coordinating data preprocessing tasks and feature engineering strategies.',
            groupAssignmentId: groupAssignments[2].id,
            threadUserIds: [
              students[0]?.id,
              students[1]?.id,
              students[2]?.id,
            ].filter(Boolean),
            createdBy: students[0]?.id,
          },
          {
            title: 'Model Evaluation & Deployment',
            description:
              'Discussion about model evaluation metrics and deployment strategies.',
            groupAssignmentId: groupAssignments[2].id,
            threadUserIds: [
              instructor.id,
              students[1]?.id,
              students[2]?.id,
            ].filter(Boolean),
            createdBy: students[1]?.id,
          },
        ]
      : []),

    // Additional general threads
    {
      title: 'General Q&A and Help',
      description: 'General questions and answers thread for all students.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, ...students.map((s) => s.id)].filter(
        Boolean,
      ),
      createdBy: instructor.id,
    },
    {
      title: 'Resource Sharing',
      description: 'Share useful resources, links, and documentation.',
      groupAssignmentId: groupAssignments[0]?.id || '',
      threadUserIds: [instructor.id, ...students.map((s) => s.id)].filter(
        Boolean,
      ),
      createdBy: students[0]?.id,
    },
  ];

  const savedThreads: ThreadDocument[] = [];
  for (const threadData of threadsData) {
    if (threadData.groupAssignmentId && threadData.createdBy) {
      const thread = new Thread(threadData);
      const savedThread = await thread.save();
      savedThreads.push(savedThread);
    }
  }

  console.log(`✅ Created ${savedThreads.length} threads`);
  return savedThreads;
}
