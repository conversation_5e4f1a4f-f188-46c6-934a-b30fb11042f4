import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, OneToMany, Join<PERSON><PERSON>umn } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { Assignment } from '@modules/assignments/entities/assignment.entity';
import { GroupUser } from '@modules/group-users/entities/group-user.entity';
import { GroupAssignmentCourse } from '@modules/group-assignment-courses/entities/group-assignment-course.entity';
import { Document } from '@modules/documents/entities/document.entity';

@Entity('group_assignments')
export class GroupAssignment extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  createdBy: string;

  @Column({ type: 'uuid' })
  assignmentId: string;

  // Entity relationships
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => Assignment, 'assignmentGroups', {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  assignment: Assignment;

  @OneToMany(() => GroupUser, 'group')
  groupUsers: GroupUser[];

  @OneToMany(() => Document, 'group')
  documents: Document[];

  @OneToMany(() => GroupAssignmentCourse, 'group')
  groupAssignmentCourses: GroupAssignmentCourse[];
}
