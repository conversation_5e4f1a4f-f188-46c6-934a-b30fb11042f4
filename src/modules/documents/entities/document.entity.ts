import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@src/modules/assignment-groups/entities/assignment-group.entity';
import { Course } from '@modules/courses/entities/course.entity';

export enum DocumentStatus {
  VISIBLE = 'visible',
  HIDDEN = 'hidden',
}

export enum DocumentRepositoryType {
  TEMPORARY = 'temporary',
  PERMANENT = 'permanent',
}

@Entity('documents')
export class Document extends BaseEntity {
  @Column({ type: 'varchar', length: 500 })
  fileUrl: string;

  @Column({ type: 'varchar', length: 255 })
  fileName: string;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.HIDDEN,
  })
  status: DocumentStatus;

  @Column({ type: 'varchar', length: 120, nullable: true })
  description: string;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'varchar', length: 255, nullable: true })
  threadId: string;

  @Column({
    type: 'enum',
    enum: DocumentRepositoryType,
    default: DocumentRepositoryType.TEMPORARY,
  })
  repositoryType: DocumentRepositoryType;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  uploaderId: string;

  @Column({ type: 'uuid' })
  groupId: string;

  @Column({ type: 'uuid' })
  courseId: string;

  @Column({ type: 'uuid' })
  assignmentGroupId: string;

  // Entity relationships
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn()
  uploader: User;

  @ManyToOne(() => GroupAssignment, 'documents', { onDelete: 'CASCADE' })
  @JoinColumn()
  group: GroupAssignment;

  @ManyToOne(() => Course, { onDelete: 'CASCADE' })
  @JoinColumn()
  course: Course;

  @ManyToOne(() => GroupAssignment, { onDelete: 'CASCADE' })
  @JoinColumn()
  assignmentGroup: GroupAssignment;
}
