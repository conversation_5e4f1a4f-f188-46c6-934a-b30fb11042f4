import { <PERSON><PERSON><PERSON>, Colum<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { Course } from '@modules/courses/entities/course.entity';

@Entity('course_users')
export class CourseUser extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  courseId: string;

  @Column({ type: 'uuid' })
  userId: string;

  // Entity relationships
  @ManyToOne(() => Course, 'courseUsers', { onDelete: 'CASCADE' })
  @JoinColumn()
  course: Course;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn()
  user: User;
}
