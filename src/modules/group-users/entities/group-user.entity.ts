import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ate<PERSON><PERSON>umn,
} from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@src/modules/assignment-groups/entities/assignment-group.entity';

@Entity('group_users')
export class GroupUser extends BaseEntity {
  @Column({ type: 'varchar', length: 100 })
  role: string;

  @CreateDateColumn()
  joinedAt: Date;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  groupId: string;

  @Column({ type: 'uuid' })
  userId: string;

  // Entity relationships
  @ManyToOne(() => GroupAssignment, 'groupUsers', { onDelete: 'CASCADE' })
  @JoinColumn()
  group: GroupAssignment;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn()
  user: User;
}
