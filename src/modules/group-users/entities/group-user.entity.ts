import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ate<PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';

@Entity('group_users')
export class GroupUser extends BaseEntity {
  @Column({ type: 'varchar', length: 100 })
  role: string;

  @CreateDateColumn()
  joinedAt: Date;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  groupId: string;

  @Column({ type: 'uuid' })
  userId: string;

  // Entity relationships
  @ManyToOne(() => GroupAssignment, 'groupUsers', { onDelete: 'CASCADE' })
  @JoinColumn()
  group: GroupAssignment;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn()
  user: User;
}
