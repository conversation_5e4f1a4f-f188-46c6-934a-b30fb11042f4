import { <PERSON><PERSON><PERSON>, Column, OneToMany } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { GroupAssignment } from '@src/modules/assignment-groups/entities/assignment-group.entity';
import { CourseUser } from '@modules/course-users/entities/course-user.entity';
import { GroupAssignmentCourse } from '@modules/group-assignment-courses/entities/group-assignment-course.entity';
import { Assignment } from '@modules/assignments/entities/assignment.entity';

@Entity('courses')
export class Course extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  // Relationships
  @OneToMany(() => Assignment, 'course')
  assignments: Assignment[];

  @OneToMany(() => GroupAssignment, 'course')
  groupAssignments: GroupAssignment[];

  @OneToMany(() => CourseUser, 'course')
  courseUsers: CourseUser[];

  @OneToMany(() => GroupAssignmentCourse, 'course')
  groupAssignmentCourses: GroupAssignmentCourse[];
}
