import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { GroupAssignment } from '@modules/group-assignments/entities/group-assignment.entity';
import { Course } from '@modules/courses/entities/course.entity';

@Entity('group_assignment_courses')
export class GroupAssignmentCourse extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  groupId: string;

  @Column({ type: 'uuid' })
  courseId: string;

  @Column({ type: 'uuid' })
  createdBy: string;

  // Entity relationships
  @ManyToOne(() => GroupAssignment, 'groupAssignmentCourses', {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  group: GroupAssignment;

  @ManyToOne(() => Course, 'groupAssignmentCourses', { onDelete: 'CASCADE' })
  @JoinColumn()
  course: Course;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
