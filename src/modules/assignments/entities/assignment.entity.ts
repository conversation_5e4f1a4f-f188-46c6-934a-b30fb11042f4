import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, OneToMany, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from '@common/base/entities/base.entity';
import { User } from '@modules/users/entities/user.entity';
import { Course } from '@modules/courses/entities/course.entity';
import { GroupAssignment } from '@src/modules/assignment-groups/entities/assignment-group.entity';

@Entity('assignments')
export class Assignment extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'timestamp', nullable: true })
  deadline: Date;

  // Foreign key relationships
  @Column({ type: 'uuid' })
  courseId: string;

  @Column({ type: 'uuid' })
  createdBy: string;

  // Entity relationships
  @ManyToOne(() => Course, 'assignments', { onDelete: 'CASCADE' })
  @JoinColumn()
  course: Course;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @OneToMany(() => GroupAssignment, 'assignment')
  assignmentGroups: GroupAssignment[];
}
